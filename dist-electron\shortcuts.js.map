{"version": 3, "file": "shortcuts.js", "sourceRoot": "", "sources": ["../electron/shortcuts.ts"], "names": [], "mappings": ";;;AAAA,uCAA8C;AAG9C,MAAa,eAAe;IAClB,QAAQ,CAAU;IAE1B,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC1B,CAAC;IAEM,uBAAuB;QAC5B,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAA;YAChD,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;gBACnC,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;oBAC3D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAA;oBACnE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,EAAE;wBAC9C,IAAI,EAAE,cAAc;wBACpB,OAAO;qBACR,CAAC,CAAA;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;gBACrD,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,yBAAc,CAAC,QAAQ,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAA;QAC3D,CAAC,CAAC,CAAA;QAEF,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,GAAG,CACT,iEAAiE,CAClE,CAAA;YAED,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAA;YAEtD,+BAA+B;YAC/B,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAA;YAE3B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;YAE9B,mCAAmC;YACnC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAE9B,oDAAoD;YACpD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAA;YAChD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YAC3C,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,sCAAsC;QACtC,yBAAc,CAAC,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAA;YAC/D,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;QAChC,CAAC,CAAC,CAAA;QAEF,yBAAc,CAAC,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;YACjE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAA;QACjC,CAAC,CAAC,CAAA;QACF,yBAAc,CAAC,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAA;YAC/D,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;QAChC,CAAC,CAAC,CAAA;QACF,yBAAc,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAClD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAA;YAC3D,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAA;QAC9B,CAAC,CAAC,CAAA;QAEF,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACjD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAA;YAChC,2DAA2D;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAA;YAChD,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC7C,yCAAyC;gBACzC,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAClC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;oBACzC,wCAAwC;oBACxC,UAAU,CAAC,GAAG,EAAE;wBACd,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;4BAC5C,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;wBAC7C,CAAC;oBACH,CAAC,EAAE,GAAG,CAAC,CAAA;gBACT,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,qCAAqC;QACrC,cAAG,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;YACvB,yBAAc,CAAC,aAAa,EAAE,CAAA;QAChC,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AA9FD,0CA8FC"}