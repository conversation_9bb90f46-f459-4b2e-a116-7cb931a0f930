{"version": 3, "file": "ipcHandlers.js", "sourceRoot": "", "sources": ["../electron/ipcHandlers.ts"], "names": [], "mappings": ";AAAA,iBAAiB;;AAKjB,sDAuGC;AA1GD,uCAAuC;AAGvC,SAAgB,qBAAqB,CAAC,QAAkB;IACtD,kBAAO,CAAC,MAAM,CACZ,2BAA2B,EAC3B,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAqC,EAAE,EAAE;QACpE,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;YACpB,QAAQ,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACH,CAAC,CACF,CAAA;IAED,kBAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE;QAChE,OAAO,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC,CAAC,CAAA;IAEF,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAC3C,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAA;YACtD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAA;YAC9D,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,CAAA;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAC3C,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QACzC,IAAI,CAAC;YACH,IAAI,QAAQ,GAAG,EAAE,CAAA;YACjB,IAAI,QAAQ,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE,CAAC;gBACnC,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAC1B,QAAQ,CAAC,kBAAkB,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;oBACjD,IAAI;oBACJ,OAAO,EAAE,MAAM,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC;iBAC9C,CAAC,CAAC,CACJ,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAC1B,QAAQ,CAAC,uBAAuB,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;oBACtD,IAAI;oBACJ,OAAO,EAAE,MAAM,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC;iBAC9C,CAAC,CAAC,CACJ,CAAA;YACH,CAAC;YACD,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;YAC7D,OAAO,QAAQ,CAAA;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YAClD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QACzC,QAAQ,CAAC,gBAAgB,EAAE,CAAA;IAC7B,CAAC,CAAC,CAAA;IAEF,kBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QACxC,IAAI,CAAC;YACH,QAAQ,CAAC,WAAW,EAAE,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;YACnD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC/C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAA;QACjD,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,mDAAmD;IACnD,kBAAO,CAAC,MAAM,CAAC,sBAAsB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAY,EAAE,QAAgB,EAAE,EAAE;QACrF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YACjF,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;YAC9D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,iDAAiD;IACjD,kBAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE;QACjE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;YACrE,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAA;YAC5D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,iDAAiD;IACjD,kBAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE;QACjE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;YACpF,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAA;YAC5D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,kBAAO,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,EAAE;QAC9B,cAAG,CAAC,IAAI,EAAE,CAAA;IACZ,CAAC,CAAC,CAAA;AACJ,CAAC"}