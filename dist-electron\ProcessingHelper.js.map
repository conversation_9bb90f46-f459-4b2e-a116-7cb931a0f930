{"version": 3, "file": "ProcessingHelper.js", "sourceRoot": "", "sources": ["../electron/ProcessingHelper.ts"], "names": [], "mappings": ";AAAA,sBAAsB;;;;;;AAGtB,2CAAuC;AACvC,oDAA2B;AAE3B,gBAAM,CAAC,MAAM,EAAE,CAAA;AAEf,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAA;AACpD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM,CAAA;AACpD,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAA;AAExE,MAAa,gBAAgB;IACnB,QAAQ,CAAU;IAClB,SAAS,CAAW;IACpB,gCAAgC,GAA2B,IAAI,CAAA;IAC/D,qCAAqC,GAA2B,IAAI,CAAA;IAE5E,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAA;QACzC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;QACtE,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAA;QAChD,IAAI,CAAC,UAAU;YAAE,OAAM;QAEvB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;QAEpC,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YACrB,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAA;YAChF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAA;gBAC3E,OAAM;YACR,CAAC;YAED,4CAA4C;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAC;YAC1E,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC/C,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3D,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBAC3E,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;oBACpE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;oBAC5F,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,iBAAiB,EAAE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC5I,OAAO;gBACT,CAAC;gBAAC,OAAO,GAAQ,EAAE,CAAC;oBAClB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;oBAC9C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;oBACjG,OAAO;gBACT,CAAC;YACH,CAAC;YAED,oDAAoD;YACpD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAA;YAC1E,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;YAClC,IAAI,CAAC,gCAAgC,GAAG,IAAI,eAAe,EAAE,CAAA;YAC7D,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBACpE,MAAM,WAAW,GAAG;oBAClB,iBAAiB,EAAE,WAAW,CAAC,IAAI;oBACnC,YAAY,EAAE,EAAE,WAAW,EAAE,2BAA2B,EAAE,UAAU,EAAE,EAAW,EAAE;oBACnF,aAAa,EAAE,EAAE,WAAW,EAAE,2BAA2B,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE;oBAC5F,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;oBACzC,UAAU,EAAE,EAAW;oBACvB,eAAe,EAAE,QAAQ;oBACzB,UAAU,EAAE,QAAQ;iBACrB,CAAC;gBACF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;gBAC5F,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;gBAC/C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YACpG,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAA;YAC9C,CAAC;YACD,OAAO;QACT,CAAC;aAAM,CAAC;YACN,aAAa;YACb,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,EAAE,CAAA;YAC1F,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;gBAC9C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAA;gBAC3E,OAAM;YACR,CAAC;YAED,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;YACxE,IAAI,CAAC,qCAAqC,GAAG,IAAI,eAAe,EAAE,CAAA;YAElE,IAAI,CAAC;gBACH,wCAAwC;gBACxC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;gBAClD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;gBAC9C,CAAC;gBAED,kCAAkC;gBAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAA;gBAC1E,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAA;gBAEjD,wCAAwC;gBACxC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAC9D,WAAW,EACX,WAAW,EACX,oBAAoB,CACrB,CAAA;gBAED,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;gBAClC,UAAU,CAAC,WAAW,CAAC,IAAI,CACzB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,aAAa,EAC7C,WAAW,CACZ,CAAA;YAEH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;gBAC/C,UAAU,CAAC,WAAW,CAAC,IAAI,CACzB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,EAC3C,KAAK,CAAC,OAAO,CACd,CAAA;YACH,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAA;YACnD,CAAC;QACH,CAAC;IACH,CAAC;IAEM,qBAAqB;QAC1B,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAC1C,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAA;YAC7C,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAA;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,qCAAqC,EAAE,CAAC;YAC/C,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAA;YAClD,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;IACrC,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,QAAgB;QAC5D,wDAAwD;QACxD,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED,mCAAmC;IAC5B,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF;AAjJD,4CAiJC"}