{"version": 3, "file": "LLMHelper.js", "sourceRoot": "", "sources": ["../electron/LLMHelper.ts"], "names": [], "mappings": ";;;;;;AAAA,yDAA2E;AAC3E,4CAAmB;AAEnB,MAAa,SAAS;IACZ,KAAK,CAAiB;IACb,YAAY,GAAG,oXAAoX,CAAA;IAEpZ,YAAY,MAAc;QACxB,MAAM,KAAK,GAAG,IAAI,kCAAkB,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAA;IACtE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,SAAiB;QAClD,MAAM,SAAS,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;QACvD,OAAO;YACL,UAAU,EAAE;gBACV,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAClC,QAAQ,EAAE,WAAW;aACtB;SACF,CAAA;IACH,CAAC;IAEO,iBAAiB,CAAC,IAAY;QACpC,+CAA+C;QAC/C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACjE,yCAAyC;QACzC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,UAAoB;QACxD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAE7F,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY;;;;;2FAKkD,CAAA;YAErF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAA;YACxE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAA;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;YACpD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;YAC7D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,WAAgB;QAC5C,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,yCAAyC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;2FAQzB,CAAA;QAEvF,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;YACvD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAA;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC/B,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAA;YACvD,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAAC,WAAgB,EAAE,WAAmB,EAAE,eAAyB;QACnG,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAElG,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,wEAAwE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,0CAA0C,WAAW;;;;;;;;2FAQ/G,CAAA;YAErF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAA;YACxE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAA;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC/B,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,MAAM,CAAC,CAAA;YAC7D,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;YAC7D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC7C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG;gBAChB,UAAU,EAAE;oBACV,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAClC,QAAQ,EAAE,WAAW;iBACtB;aACF,CAAC;YACF,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,4QAA4Q,CAAC;YAChT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACrE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC;YACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC7B,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,IAAY,EAAE,QAAgB;QAChE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG;gBAChB,UAAU,EAAE;oBACV,IAAI;oBACJ,QAAQ;iBACT;aACF,CAAC;YACF,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,2RAA2R,CAAC;YAC/T,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACrE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC;YACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC7B,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC7C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG;gBAChB,UAAU,EAAE;oBACV,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAClC,QAAQ,EAAE,WAAW;iBACtB;aACF,CAAC;YACF,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,4SAA4S,CAAC;YAChV,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACrE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC;YACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC7B,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA9JD,8BA8JC"}