{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../electron/main.ts"], "names": [], "mappings": ";;;AAAA,uCAA6C;AAC7C,+CAAqD;AACrD,iDAA6C;AAC7C,yDAAqD;AACrD,2CAA6C;AAC7C,yDAAqD;AAErD,MAAa,QAAQ;IACX,MAAM,CAAC,QAAQ,GAAoB,IAAI,CAAA;IAEvC,YAAY,CAAc;IAC1B,gBAAgB,CAAkB;IACnC,eAAe,CAAiB;IAChC,gBAAgB,CAAkB;IAEzC,kBAAkB;IACV,IAAI,GAA0B,OAAO,CAAA;IAErC,WAAW,GAMR,IAAI,CAAA,CAAC,aAAa;IAErB,WAAW,GAAY,KAAK,CAAA;IAEpC,oBAAoB;IACJ,iBAAiB,GAAG;QAClC,eAAe;QACf,YAAY,EAAE,wBAAwB;QACtC,cAAc,EAAE,2BAA2B;QAE3C,4CAA4C;QAC5C,aAAa,EAAE,eAAe;QAC9B,iBAAiB,EAAE,mBAAmB;QACtC,gBAAgB,EAAE,kBAAkB;QACpC,sBAAsB,EAAE,gBAAgB;QAExC,qCAAqC;QACrC,WAAW,EAAE,aAAa;QAC1B,aAAa,EAAE,eAAe;QAC9B,WAAW,EAAE,aAAa;KAClB,CAAA;IAEV;QACE,oCAAoC;QACpC,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,CAAA;QAE1C,8BAA8B;QAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEvD,8BAA8B;QAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,IAAI,CAAC,CAAA;QAElD,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,2BAAe,CAAC,IAAI,CAAC,CAAA;IAClD,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACvB,QAAQ,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAA;QACpC,CAAC;QACD,OAAO,QAAQ,CAAC,QAAQ,CAAA;IAC1B,CAAC;IAED,sBAAsB;IACf,aAAa;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAA;IAC1C,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAEM,OAAO,CAAC,IAA2B;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAA;IACtC,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAC9B,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAEM,cAAc,CAAC,WAAgB;QACpC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;IAChC,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAA;IACnD,CAAC;IAEM,uBAAuB;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAA;IACxD,CAAC;IAED,4BAA4B;IACrB,YAAY;QACjB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAA;IAClC,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;IACpC,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;IACpC,CAAC;IAEM,gBAAgB;QACrB,OAAO,CAAC,GAAG,CACT,eAAe,EACf,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EACjD,qBAAqB,EACrB,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,MAAM,CACvD,CAAA;QACD,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAA;IACtC,CAAC;IAEM,mBAAmB,CAAC,KAAa,EAAE,MAAc;QACtD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IACtD,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAA;QAEnC,qBAAqB;QACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QAEvB,8BAA8B;QAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IACvB,CAAC;IAED,gCAAgC;IACzB,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAEtE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAC/D,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,EAC3B,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAC5B,CAAA;QAED,OAAO,cAAc,CAAA;IACvB,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;IACxD,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,IAAY;QAEZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;IACrD,CAAC;IAED,iCAAiC;IAC1B,cAAc;QACnB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;IACpC,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAA;IACrC,CAAC;IACM,cAAc;QACnB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;IACpC,CAAC;IACM,YAAY;QACjB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAA;IAClC,CAAC;IAEM,cAAc,CAAC,KAAc;QAClC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;IAC1B,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;;AAlLH,4BAmLC;AAED,6BAA6B;AAC7B,KAAK,UAAU,aAAa;IAC1B,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAA;IAEvC,iDAAiD;IACjD,IAAA,mCAAqB,EAAC,QAAQ,CAAC,CAAA;IAE/B,cAAG,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;QACxB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAC3B,QAAQ,CAAC,YAAY,EAAE,CAAA;QACvB,kDAAkD;QAClD,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAA;IACpD,CAAC,CAAC,CAAA;IAEF,cAAG,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;QACtB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QAC5B,IAAI,QAAQ,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE,CAAC;YACtC,QAAQ,CAAC,YAAY,EAAE,CAAA;QACzB,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,oDAAoD;IACpD,cAAG,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC/B,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClC,cAAG,CAAC,IAAI,EAAE,CAAA;QACZ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,cAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,4BAA4B;IAC7C,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,qCAAqC,CAAC,CAAA;AACrE,CAAC;AAED,wBAAwB;AACxB,aAAa,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA"}