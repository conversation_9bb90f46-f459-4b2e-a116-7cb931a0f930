{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../electron/preload.ts"], "names": [], "mappings": ";;;AAAA,uCAAqD;AAoCxC,QAAA,iBAAiB,GAAG;IAC/B,eAAe;IACf,YAAY,EAAE,wBAAwB;IACtC,cAAc,EAAE,2BAA2B;IAE3C,4CAA4C;IAC5C,aAAa,EAAE,eAAe;IAC9B,iBAAiB,EAAE,mBAAmB;IACtC,gBAAgB,EAAE,kBAAkB;IACpC,sBAAsB,EAAE,gBAAgB;IAExC,qCAAqC;IACrC,WAAW,EAAE,aAAa;IAC1B,aAAa,EAAE,eAAe;IAC9B,WAAW,EAAE,aAAa;CAClB,CAAA;AAEV,kDAAkD;AAClD,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE;IAC7C,uBAAuB,EAAE,CAAC,UAA6C,EAAE,EAAE,CACzE,sBAAW,CAAC,MAAM,CAAC,2BAA2B,EAAE,UAAU,CAAC;IAC7D,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC;IAC3D,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC;IAC3D,gBAAgB,EAAE,CAAC,IAAY,EAAE,EAAE,CACjC,sBAAW,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAE/C,kBAAkB;IAClB,iBAAiB,EAAE,CACjB,QAA2D,EAC3D,EAAE;QACF,MAAM,YAAY,GAAG,CAAC,CAAM,EAAE,IAAuC,EAAE,EAAE,CACvE,QAAQ,CAAC,IAAI,CAAC,CAAA;QAChB,sBAAW,CAAC,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAA;QAChD,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAA;QAC9D,CAAC,CAAA;IACH,CAAC;IACD,gBAAgB,EAAE,CAAC,QAAqC,EAAE,EAAE;QAC1D,MAAM,YAAY,GAAG,CAAC,CAAM,EAAE,SAAiB,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;QACvE,sBAAW,CAAC,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAA;QAC/C,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAA;QAC7D,CAAC,CAAA;IACH,CAAC;IACD,WAAW,EAAE,CAAC,QAAoB,EAAE,EAAE;QACpC,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAA;QACrC,sBAAW,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QAC1C,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QACxD,CAAC,CAAA;IACH,CAAC;IACD,eAAe,EAAE,CAAC,QAAoB,EAAE,EAAE;QACxC,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAA;QACrC,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,aAAa,EAAE,YAAY,CAAC,CAAA;QAC7D,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,yBAAiB,CAAC,aAAa,EAAE,YAAY,CAAC,CAAA;QAC3E,CAAC,CAAA;IACH,CAAC;IACD,YAAY,EAAE,CAAC,QAAoB,EAAE,EAAE;QACrC,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAA;QACrC,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QAC3D,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,yBAAiB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QACzE,CAAC,CAAA;IACH,CAAC;IAED,cAAc,EAAE,CAAC,QAA6B,EAAE,EAAE;QAChD,sBAAW,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QACjE,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAC3D,QAAQ,CAAC,IAAI,CAAC,CACf,CAAA;QACH,CAAC,CAAA;IACH,CAAC;IACD,YAAY,EAAE,CAAC,QAAiC,EAAE,EAAE;QAClD,MAAM,YAAY,GAAG,CAAC,CAAM,EAAE,KAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC/D,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QAC3D,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,yBAAiB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QACzE,CAAC,CAAA;IACH,CAAC;IACD,eAAe,EAAE,CAAC,QAAiC,EAAE,EAAE;QACrD,MAAM,YAAY,GAAG,CAAC,CAAM,EAAE,KAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC/D,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAA;QACtE,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CACxB,yBAAiB,CAAC,sBAAsB,EACxC,YAAY,CACb,CAAA;QACH,CAAC,CAAA;IACH,CAAC;IACD,yBAAyB,EAAE,CAAC,QAAoB,EAAE,EAAE;QAClD,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAA;QACrC,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAA;QAC9D,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,yBAAiB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAA;QAC5E,CAAC,CAAA;IACH,CAAC;IAED,kBAAkB,EAAE,CAAC,QAA6B,EAAE,EAAE;QACpD,MAAM,YAAY,GAAG,CAAC,CAAM,EAAE,IAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1D,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAA;QACjE,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CACxB,yBAAiB,CAAC,iBAAiB,EACnC,YAAY,CACb,CAAA;QACH,CAAC,CAAA;IACH,CAAC;IACD,iBAAiB,EAAE,CAAC,QAA6B,EAAE,EAAE;QACnD,MAAM,YAAY,GAAG,CAAC,CAAM,EAAE,IAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1D,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAA;QAChE,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CACxB,yBAAiB,CAAC,gBAAgB,EAClC,YAAY,CACb,CAAA;QACH,CAAC,CAAA;IACH,CAAC;IACD,cAAc,EAAE,CAAC,QAAoB,EAAE,EAAE;QACvC,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAA;QACrC,sBAAW,CAAC,EAAE,CAAC,yBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QAC5D,OAAO,GAAG,EAAE;YACV,sBAAW,CAAC,cAAc,CAAC,yBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QAC1E,CAAC,CAAA;IACH,CAAC;IACD,cAAc,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC5D,eAAe,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,mBAAmB,CAAC;IAC9D,sBAAsB,EAAE,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,sBAAsB,EAAE,IAAI,EAAE,QAAQ,CAAC;IACtH,gBAAgB,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC;IAClF,gBAAgB,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC;IAClF,OAAO,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,UAAU,CAAC;CAC/B,CAAC,CAAA"}