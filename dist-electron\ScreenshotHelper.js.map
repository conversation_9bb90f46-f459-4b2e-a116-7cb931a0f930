{"version": 3, "file": "ScreenshotHelper.js", "sourceRoot": "", "sources": ["../electron/ScreenshotHelper.ts"], "names": [], "mappings": ";AAAA,sBAAsB;;;;;;AAEtB,0DAA4B;AAC5B,sDAAwB;AACxB,uCAA8B;AAC9B,+BAAmC;AACnC,4EAA2C;AAE3C,MAAa,gBAAgB;IACnB,eAAe,GAAa,EAAE,CAAA;IAC9B,oBAAoB,GAAa,EAAE,CAAA;IAC1B,eAAe,GAAG,CAAC,CAAA;IAEnB,aAAa,CAAQ;IACrB,kBAAkB,CAAQ;IAEnC,IAAI,GAA0B,OAAO,CAAA;IAE7C,YAAY,OAA8B,OAAO;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAEhB,yBAAyB;QACzB,IAAI,CAAC,aAAa,GAAG,mBAAI,CAAC,IAAI,CAAC,cAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,aAAa,CAAC,CAAA;QACtE,IAAI,CAAC,kBAAkB,GAAG,mBAAI,CAAC,IAAI,CACjC,cAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EACvB,mBAAmB,CACpB,CAAA;QAED,yCAAyC;QACzC,IAAI,CAAC,iBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YACvC,iBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAClC,CAAC;QACD,IAAI,CAAC,iBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC5C,iBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAEM,OAAO,CAAC,IAA2B;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAEM,uBAAuB;QAC5B,OAAO,IAAI,CAAC,oBAAoB,CAAA;IAClC,CAAC;IAEM,WAAW;QAChB,wBAAwB;QACxB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YAC9C,iBAAE,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE;gBAChC,IAAI,GAAG;oBACL,OAAO,CAAC,KAAK,CAAC,gCAAgC,cAAc,GAAG,EAAE,GAAG,CAAC,CAAA;YACzE,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QAEzB,6BAA6B;QAC7B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACnD,iBAAE,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,EAAE;gBAChC,IAAI,GAAG;oBACL,OAAO,CAAC,KAAK,CACX,sCAAsC,cAAc,GAAG,EACvD,GAAG,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAA;IAChC,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,cAA0B,EAC1B,cAA0B;QAE1B,cAAc,EAAE,CAAA;QAChB,IAAI,cAAc,GAAG,EAAE,CAAA;QAEvB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC1B,cAAc,GAAG,mBAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,IAAA,SAAM,GAAE,MAAM,CAAC,CAAA;YACjE,MAAM,IAAA,4BAAU,EAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAA;YAE9C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACzC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvD,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;gBAChD,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC;wBACH,MAAM,iBAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;oBACvC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,cAAc,GAAG,mBAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAA,SAAM,GAAE,MAAM,CAAC,CAAA;YACtE,MAAM,IAAA,4BAAU,EAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAA;YAE9C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YAC9C,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAA;gBACrD,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC;wBACH,MAAM,iBAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;oBACvC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,cAAc,EAAE,CAAA;QAChB,OAAO,cAAc,CAAA;IACvB,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,iBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YACjD,OAAO,yBAAyB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAA;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,IAAY;QAEZ,IAAI,CAAC;YACH,MAAM,iBAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAChD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,KAAK,IAAI,CAChC,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAC1D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,KAAK,IAAI,CAChC,CAAA;YACH,CAAC;YACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAA;QACjD,CAAC;IACH,CAAC;CACF;AA7ID,4CA6IC"}