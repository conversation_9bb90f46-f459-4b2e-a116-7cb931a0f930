{"version": 3, "file": "WindowHelper.js", "sourceRoot": "", "sources": ["../electron/WindowHelper.ts"], "names": [], "mappings": ";;;;;;AACA,uCAAgD;AAEhD,0DAA4B;AAE5B,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAA;AAEpD,MAAM,QAAQ,GAAG,KAAK;IACpB,CAAC,CAAC,uBAAuB;IACzB,CAAC,CAAC,UAAU,mBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,EAAE,CAAA;AAE1D,MAAa,YAAY;IACf,UAAU,GAAyB,IAAI,CAAA;IACvC,eAAe,GAAY,KAAK,CAAA;IAChC,cAAc,GAAoC,IAAI,CAAA;IACtD,UAAU,GAA6C,IAAI,CAAA;IAC3D,QAAQ,CAAU;IAE1B,mDAAmD;IAC3C,WAAW,GAAW,CAAC,CAAA;IACvB,YAAY,GAAW,CAAC,CAAA;IACxB,IAAI,GAAW,CAAC,CAAA;IAChB,QAAQ,GAAW,CAAC,CAAA;IACpB,QAAQ,GAAW,CAAC,CAAA;IAE5B,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC1B,CAAC;IAEM,mBAAmB,CAAC,KAAa,EAAE,MAAc;QACtD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YAAE,OAAM;QAE7D,8BAA8B;QAC9B,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAA;QAE1D,wBAAwB;QACxB,MAAM,cAAc,GAAG,iBAAM,CAAC,iBAAiB,EAAE,CAAA;QACjD,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,CAAA;QAE5C,6DAA6D;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAChC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAC/D,CAAA;QAED,yEAAyE;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,EAAE,eAAe,CAAC,CAAA;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAEnC,2DAA2D;QAC3D,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAA;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QAElD,uBAAuB;QACvB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;YACxB,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,QAAQ;YACX,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE,SAAS;SAClB,CAAC,CAAA;QAEF,wBAAwB;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAA;QAC9C,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAA;QACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;IACtB,CAAC;IAEM,YAAY;QACjB,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;YAAE,OAAM;QAEpC,MAAM,cAAc,GAAG,iBAAM,CAAC,iBAAiB,EAAE,CAAA;QACjD,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,CAAA;QAC5C,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAA;QACjC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAA;QAEnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA,CAAC,WAAW;QACzD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA,CAAC,oBAAoB;QAEtC,MAAM,cAAc,GAA6C;YAC/D,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;YACnB,CAAC,EAAE,IAAI,CAAC,QAAQ;YAChB,CAAC,EAAE,CAAC;YACJ,cAAc,EAAE;gBACd,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,OAAO,EAAE,mBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;aAC5C;YACD,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,IAAI;YACjB,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,IAAI;YACjB,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,KAAK;YAChB,eAAe,EAAE,WAAW;YAC5B,SAAS,EAAE,IAAI;SAChB,CAAA;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,wBAAa,CAAC,cAAc,CAAC,CAAA;QACnD,6CAA6C;QAC7C,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;QAE1C,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,EAAE;gBAC9C,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAA;YACF,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA;YAC/C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;QAClD,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,oDAAoD;YACpD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBACjC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;YACrC,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QACrC,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAEpC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9C,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAA;QAC1C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAA;QAClD,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAA;QAChE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAA;QAExB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;IAC7B,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAE5B,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;YAC9B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAA;gBAC1C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAA;gBAClD,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAA;gBACxB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAA;YAC1B,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAA;gBAC1C,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAA;YAClE,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACtB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAA;YAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACxB,CAAC,CAAC,CAAA;IACJ,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAA;YAC3D,OAAM;QACR,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAA;QAC1C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAA;QAClD,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAA;QAChE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAA;QACtB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAA;IAC9B,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAA;YAC3D,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;gBACxB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxB,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;gBAC5B,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;aAC/B,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAA;QAE9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;IAC7B,CAAC;IAEM,gBAAgB;QACrB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,CAAA;QACvB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,EAAE,CAAA;QACvB,CAAC;IACH,CAAC;IAED,kCAAkC;IAC3B,eAAe;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAE5B,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,CAAA;QAC/C,MAAM,SAAS,GAAG,WAAW,GAAG,CAAC,CAAA;QAEjC,2CAA2C;QAC3C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAE1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,WAAW,GAAG,SAAS,EAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAC1B,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAC1B,CAAA;IACH,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAE5B,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,CAAA;QAC/C,MAAM,SAAS,GAAG,WAAW,GAAG,CAAC,CAAA;QAEjC,2CAA2C;QAC3C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAE1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/D,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAC1B,CAAA;IACH,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAE5B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAA;QACjD,MAAM,UAAU,GAAG,YAAY,GAAG,CAAC,CAAA;QAEnC,2CAA2C;QAC3C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAE1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,YAAY,GAAG,UAAU,EAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAC1B,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAC1B,CAAA;IACH,CAAC;IAEM,YAAY;QACjB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAE5B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAA;QACjD,MAAM,UAAU,GAAG,YAAY,GAAG,CAAC,CAAA;QAEnC,2CAA2C;QAC3C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAE1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAC1B,CAAA;IACH,CAAC;CACF;AAhRD,oCAgRC"}